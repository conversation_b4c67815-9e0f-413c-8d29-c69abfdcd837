'use server';

import { createAction, createAdminAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import { GymManagerInvitation, GymManagerInvitationWithDetails } from '@/types/gym-manager-invitations';
import { z } from 'zod';



// Validation schemas
const createInvitationSchema = z.object({
  gymId: z.string().uuid('Geçerli bir salon ID gereklidir'),
  companyId: z.string().uuid('Geçerli bir şirket ID gereklidir'),
});

const validateInviteCodeSchema = z.object({
  inviteCode: z.string().length(8, 'Davet kodu 8 karakter olmalıdır'),
});

/**
 * Gym manager davet kodu oluşturur
 */
export async function createGymManagerInvitation(
  data: z.infer<typeof createInvitationSchema>
): Promise<ApiResponse<GymManagerInvitation>> {
  const validatedData = createInvitationSchema.parse(data);
  const { gymId, companyId } = validatedData;

  return createAction<GymManagerInvitation>(
    async (_, supabase, userId) => {
      // Gym'in company'ye ait olduğunu ve kullanıcının company manager olduğunu kontrol et
      const { data: gym, error: gymError } = await supabase
        .from('gyms')
        .select(
          `
          id,
          name,
          company:companies!inner(
            id,
            manager_profile_id
          )
        `
        )
        .eq('id', gymId)
        .eq('company.manager_profile_id', userId)
        .single();

      if (gymError || !gym) {
        throw new Error('Bu salona davet kodu oluşturma yetkiniz bulunmuyor');
      }

      // Aktif davet kodu var mı kontrol et
      const { data: existingInvitation } = await supabase
        .from('gym_manager_invitations')
        .select('id')
        .eq('gym_id', gymId)
        .eq('status', 'pending')
        .gt('expires_at', new Date().toISOString())
        .maybeSingle();

      if (existingInvitation) {
        throw new Error('Bu salon için zaten aktif bir davet kodu bulunuyor');
      }

      // Yeni davet kodu oluştur
      const { data: invitation, error: createError } = await supabase
        .from('gym_manager_invitations')
        .insert({
          company_id: companyId,
          gym_id: gymId,
        })
        .select()
        .single();

      if (createError) {
        throw new Error(`Davet kodu oluşturulamadı: ${createError.message}`);
      }

      return invitation;
    },
    {
      revalidatePaths: ['/dashboard/company/invitations'],
    }
  );
}

/**
 * Company manager'ın davet kodlarını listeler
 */
export async function getCompanyInvitations(): Promise<
  ApiResponse<GymManagerInvitationWithDetails[]>
> {
  return createAction<GymManagerInvitationWithDetails[]>(
    async (_, supabase, userId) => {
      // Önce kullanıcının company'sini al
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id')
        .eq('manager_profile_id', userId)
        .single();

      if (companyError || !company) {
        throw new Error('Şirket bilgisi bulunamadı');
      }

      const { data: invitations, error } = await supabase
        .from('gym_manager_invitations')
        .select(
          `
          *,
          company:companies(*),
          gym:gyms(name),
          user:profiles(full_name, email)
        `
        )
        .eq('company_id', company.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Davet kodları getirilemedi: ${error.message}`);
      }

      return invitations || [];
    }
  );
}

/**
 * Davet kodunu iptal eder
 */
export async function cancelInvitation(
  invitationId: string
): Promise<ApiResponse<void>> {
  return createAction<void>(
    async (_, supabase, userId) => {
      // Önce kullanıcının company'sini al
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id')
        .eq('manager_profile_id', userId)
        .single();

      if (companyError || !company) {
        throw new Error('Şirket bilgisi bulunamadı');
      }

      const { error } = await supabase
        .from('gym_manager_invitations')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString(),
        })
        .eq('id', invitationId)
        .eq('company_id', company.id)
        .eq('status', 'pending');

      if (error) {
        throw new Error(`Davet kodu iptal edilemedi: ${error.message}`);
      }
    },
    {
      revalidatePaths: ['/dashboard/company/invitations'],
    }
  );
}


/**
 * Davet kodunu kullanarak direkt olarak gym manager atar
 */
export async function redeemInvitationCode(
  data: z.infer<typeof validateInviteCodeSchema>
): Promise<ApiResponse<{ message: string; gymName: string; companyName: string }>> {
  const validatedData = validateInviteCodeSchema.parse(data);
  const { inviteCode } = validatedData;

  return createAdminAction<{
    message: string;
    gymName: string;
    companyName: string;
  }>(async (_, _supabase, userId, adminClient) => {

    // Önce davet kodunu kontrol et
    const { data: invitation, error: invitationError } = await adminClient
      .from('gym_manager_invitations')
      .select('id, gym_id, status, expires_at')
      .eq('invite_code', inviteCode)
      .eq('status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .maybeSingle();

    if (invitationError || !invitation) {
      throw new Error('Geçersiz veya süresi dolmuş davet kodu');
    }

    // Gym ve company bilgilerini al
    const { data: gym, error: gymError } = await adminClient
      .from('gyms')
      .select(
        `
          id,
          name,
          manager_profile_id,
          company_id,
          company:companies(
            id,
            name,
            manager_profile_id
          )
        `
      )
      .eq('id', invitation.gym_id)
      .maybeSingle();

    if (gymError || !gym) {
      throw new Error('Salon bilgileri alınamadı');
    }

    // Gym'in zaten bir manager'ı var mı kontrol et
    if (gym.manager_profile_id) {
      throw new Error('Bu salon zaten bir yöneticiye sahip');
    }

    // Transaction ile gym manager ata ve daveti güncelle
    const { error: updateGymError } = await adminClient
      .from('gyms')
      .update({
        manager_profile_id: userId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', invitation.gym_id);

    if (updateGymError) {
      throw new Error('Salon yöneticisi atanamadı');
    }

    // Daveti kabul edildi olarak işaretle
    const { error: updateInvitationError } = await adminClient
      .from('gym_manager_invitations')
      .update({
        status: 'accepted',
        used_by: userId,
        used_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', invitation.id);

    if (updateInvitationError) {
      throw new Error('Davet durumu güncellenemedi', updateInvitationError);
    }

    return {
      message: `${gym.name} salonuna yönetici olarak katılma işleminiz başarıyla tamamlandı! Artık ${gym.name} salonunu yönetmeye başlayabilirsiniz!`,
      gymName: gym.name,
      companyName: (gym.company as any)?.name || 'Bilinmeyen Şirket',
    };
  });
}

/**
 * Bir yöneticinin salondan atamasını kaldırır
 */
export async function removeManagerFromGym({
  gymId,
}: {
  gymId: string;
}): Promise<ApiResponse<null>> {
  return await createAction<null>(
    async (_, supabase, userId) => {
      // 1. Kullanıcının şirketini bul
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id')
        .eq('manager_profile_id', userId)
        .single();

      if (companyError || !company) {
        throw new Error('Yetkili şirket bulunamadı.');
      }

      // 2. Salonun bu şirkete ait olduğunu doğrula
      const { data: gym, error: gymError } = await supabase
        .from('gyms')
        .select('id, company_id')
        .eq('id', gymId)
        .single();

      if (gymError || !gym) {
        throw new Error('Salon bulunamadı.');
      }

      if (gym.company_id !== company.id) {
        throw new Error('Bu işlem için yetkiniz yok.');
      }

      // 3. Yöneticinin atamasını kaldır
      const { error: updateError } = await supabase
        .from('gyms')
        .update({ manager_profile_id: null })
        .eq('id', gymId);

      if (updateError) {
        throw new Error(
          'Yönetici kaldırılırken bir hata oluştu: ' + updateError.message
        );
      }

      return null;
    },
    {
      revalidatePaths: ['/dashboard/company/managers'],
    }
  );
}

